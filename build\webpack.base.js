//# webpack 公共配置

// 这里必须先执行
let nodeEnv = process.env.NODE_ENV.split(':');
const isDev = nodeEnv[0] === 'dev'; // 判断是否为开发环境
process.env.NODE_ENV = nodeEnv[1];

const path = require('path');
const fs = require('fs');
const webpack = require('webpack');
const chalk = require('chalk');
const HtmlWebpackPlugin = require('html-webpack-plugin'); // html文件生成
const ProgressBarPlugin = require('progress-bar-webpack-plugin'); //构建进度条
const FriendlyErrorsPlugin = require('friendly-errors-webpack-plugin'); // 美化控制台输出
const MiniCssExtractPlugin = require('mini-css-extract-plugin'); // 将css代码打包成一个单独的css文件
const PostcssPresetEnv = require("postcss-preset-env");
const { VueLoaderPlugin } = require('vue-loader');
const CopyPlugin = require("copy-webpack-plugin");
const envConfig = require('./webpack.env'); // 环境变量

function resolve (dir) {
  return path.resolve(__dirname, '..', dir)
}

// loading 动画
const loading = {
  html: fs.readFileSync(path.join(__dirname, '../src/assets/loading/loading.html')),
  css: '<style>' + fs.readFileSync(path.join(__dirname, '../src/assets/loading/loading.css')) + '</style>'
};

const webpackBaseConfig = {
  target: 'web',
  stats: 'errors-only',
  entry: {
    main: './src/main.js',
  },
  output: {
    publicPath: "/",
    path: resolve('dist'),
    filename: isDev ? 'static/js/[name].js':  'static/js/[name]-[contenthash].js',
    clean: true,
    // Webpack 会在输出的 bundle 中生成路径信息。然而，在打包数千个模块的项目中，这会导致造成垃圾回收性能压力。pathinfo 设置关闭
    pathinfo: false
  },
  module: {
    rules: [
      {
        // tcplayer.min.css文件中的fill-available属性有冲突, 将该文件中全部的fill-available替换成stretch
        test: /tcplayer\.min\.css$/,
        loader: 'string-replace-loader',
        options: {
          search: /fill-available/g,
          replace: 'stretch'
        }
      },
      {
        test: /\.vue$/,
        use: 'vue-loader'
      },
      {
        test: /\.js$/,
        include: [resolve('src')],
        exclude: /node_modules/,
        use: [
          'babel-loader'
        ]
      },
      {
        test: /\.(css|scss)$/,
        use: [
          //只在生产环境使用 MiniCssExtractPlugin.loader 的作用就是把css-loader处理好的样式资源（js文件内），单独提取出来成为css样式文件
          isDev ? 'style-loader':  MiniCssExtractPlugin.loader,
          'css-loader',
          // postcss-loader 添加css前缀 需要在package.json里面 browserslist 字段配置
          {
            loader: "postcss-loader",
            options: {
              postcssOptions: {
                ident: "postcss",
                plugins: [PostcssPresetEnv()]
              }
            }
          },
          'sass-loader'
        ]
      },
      {
        test: /\.less$/,
        use: [
          //只在生产环境使用 MiniCssExtractPlugin.loader 的作用就是把css-loader处理好的样式资源（js文件内），单独提取出来成为css样式文件
          isDev ? 'style-loader': MiniCssExtractPlugin.loader,
          'css-loader',
          // postcss-loader 添加css前缀 需要在package.json里面 browserslist 字段配置
          {
            loader: "postcss-loader",
            options: {
              postcssOptions: {
                ident: "postcss",
                plugins: [PostcssPresetEnv()]
              }
            }
          },
          'less-loader'
        ]
      },
      {
        test: /\.(png|jpe?g|gif|svg)(\?.*)?$/,
        type: "asset",
        generator: {
          filename: isDev ? 'static/images/[hash].[ext]' : 'static/images/[contenthash].[ext][query]'
        },
        parser: {
          dataUrlCondition: {
            maxSize: isDev ? 0 : 25 * 1024, // 生产环境超过25kb图片转换成base64
          }
        }
      },
      {
        test: /\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/,
        exclude: /node_modules/,
        type: "asset",
        generator: {
          filename: isDev ? 'static/media/[hash].[ext]' : 'static/media/[contenthash].[ext][query]'
        },
      },
      {
        test: /\.(woff2?|eot|ttf|otf)(\?.*)?$/,
        type: "asset/resource",
        generator: {
          // 输出文件位置以及文件名
          filename: isDev ? "static/fonts/[hash].[ext]" : "static/fonts/[contenthash].[ext]"
        },
      }
    ]
  },
  resolve: {
    alias: {
      '@': resolve('src'),
      'vue$': 'vue/dist/vue.esm.js',
      symlinks: false
    },
    extensions: ['.js', '.json', '.vue', '.scss'],
    // [优化] 该配置明确告诉webpack，直接去上一层找node_modules
    modules: [resolve('node_modules')],
    fallback: {
      "querystring": false,
      "crypto": false,
      "zlib": false
    }
  },
  plugins: [
    // 美化控制台输出log
    new FriendlyErrorsPlugin(),

    // 打包进度条
    new ProgressBarPlugin({
      format: chalk.green.bold('build [:bar] :percent') + chalk.blue.bold(' (:elapsed s) ')
    }),

    // 注入全局的环境变量
    new webpack.DefinePlugin({
      process: {
        env: {
          RUN_ENV: JSON.stringify(process.env.NODE_ENV),
          ...envConfig
        }
      }
    }),

    // vue 插件
    new VueLoaderPlugin(),

    // 提取 css 打包成一个单独的文件
    new MiniCssExtractPlugin({
      filename: 'static/css/[contenthash].css',
      ignoreOrder: true
    }),

    // Copy 静态文件
    new CopyPlugin({
      patterns: [
        { from: resolve('static'), to: "static" }
      ]
    }),

    // moment插件包体积
    new webpack.ContextReplacementPlugin(/moment[/\\]locale$/, /en-gb|zh-cn/),

    // 生成HTML 注入打包后的js入口代码
    new HtmlWebpackPlugin({
      template: "./public/index.html",
      filename: "index.html",
      inject: 'body', // 将js注入到body
      favicon: './public/favicon.ico',
      // hash: true, // 破坏性清除缓存
      env: process.env.NODE_ENV,
      isDev: isDev,
      loading: loading
    })
  ]
}

module.exports = webpackBaseConfig;

